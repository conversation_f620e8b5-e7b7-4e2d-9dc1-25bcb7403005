# 钉钉 MCP 服务安装和使用指南

## 项目简介

本项目是一个钉钉 MCP（Message Connector Protocol）服务，提供了与钉钉企业应用交互的 API 接口。项目基于 Go 语言开发，支持员工信息查询和消息发送等功能。

## 功能特性

- **员工管理**：获取企业员工人数和基础信息
- **消息发送**：支持文本和 Markdown 格式的企业消息发送
- **消息撤回**：支持撤回已发送的企业消息
- **MCP 协议**：完全兼容 Claude Desktop 的 MCP 协议

## 前置要求

1. **Go 语言环境**：需要 Go 1.24 或更高版本
2. **钉钉企业应用**：需要在钉钉开放平台创建企业应用
3. **Claude Desktop**：用于集成 MCP 服务

## 安装步骤

### 1. 克隆项目

```bash
git clone https://github.com/zhaoyunxing92/dingtalk-mcp.git
cd dingtalk-mcp
```

### 2. 编译项目

```bash
go mod tidy
go build -o dingtalk-mcp .
```

### 3. 钉钉应用配置

1. 访问 [钉钉开放平台](https://open-dev.dingtalk.com)
2. 创建一个企业内部应用
3. 获取以下信息：
   - `AgentId`：应用的 Agent ID
   - `AppKey`：应用的 Key
   - `AppSecret`：应用的密钥
4. 为应用配置必要的权限：
   - 通讯录只读权限
   - 企业消息发送权限

### 4. Claude Desktop 配置

将以下配置添加到 Claude Desktop 的配置文件中：

```json
{
  "mcpServers": {
    "dingtalk-mcp": {
      "command": "/path/to/dingtalk-mcp",
      "args": [],
      "env": {
        "DINGTALK_AGENT_ID": "你的AgentId",
        "DINGTALK_KEY": "你的AppKey", 
        "DINGTALK_SECRET": "你的AppSecret"
      },
      "disabled": false,
      "autoApprove": [
        "get_employees_count",
        "get_simple_employees", 
        "recall_corp_conversation",
        "send_corp_conversation",
        "send_markdown_corp_conversation"
      ],
      "timeout": 60
    }
  }
}
```

## 可用功能

| API 名称 | 功能描述 | 参数 |
|---------|----------|------|
| `get_employees_count` | 获取企业员工人数 | 无 |
| `get_simple_employees` | 获取企业员工基础信息 | 无 |
| `send_corp_conversation` | 发送文本消息 | `userIds`, `context` |
| `send_markdown_corp_conversation` | 发送 Markdown 消息 | `userIds`, `context` |
| `recall_corp_conversation` | 撤回消息 | `taskId` |

## 使用示例

在 Claude Desktop 中，你可以这样使用：

```
请帮我获取公司的员工人数
```

```
请向用户ID为 "user123" 的员工发送消息："会议将在下午2点开始"
```

```
请向用户ID为 "user123" 的员工发送 Markdown 格式的消息
```

## 故障排除

1. **命令找不到**：确保可执行文件路径正确，或将其添加到 PATH 环境变量中
2. **权限错误**：检查钉钉应用是否有足够的权限
3. **连接失败**：验证 AppKey 和 AppSecret 是否正确

## 开发和贡献

如果你想为项目贡献代码：

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 许可证

本项目使用 Apache-2.0 许可证。详见 [LICENSE](LICENSE) 文件。
